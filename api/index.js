import bodyParser from 'body-parser';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import express from 'express';
import jwt from 'jsonwebtoken';
import { JSDOM } from "jsdom";

import { createDirectus, createItem, deleteItem, readItems, rest, staticToken, updateItem, verifyHash } from '@directus/sdk';

let directus;

const DIRECTUS_URL = 'https://nhboptical.vtsuki.net';
const DIRECTUS_TOKEN = '7etaqtKX1FIIc2Oe8VouttR3I99EFJDk';
const SECRET_KEY = 'your-secret-key';
const REFRESH_SECRET_KEY = 'your-refresh-secret-key';

const app = express()
app.use(cors({ origin: 'http://localhost:5173', credentials: true }))
app.use(cookieParser());
app.use(bodyParser.json())


// 🔑 JWT generator
function generateAccessToken(payload) {
    return jwt.sign(payload, SECRET_KEY, { expiresIn: "10m" }); // short-lived
}

// 🔑 JWT generator
function generateRefreshToken(payload) {
    const token = jwt.sign(payload, REFRESH_SECRET_KEY, { expiresIn: "7d" });
    return token;
}

// ✅ Login with email + password
async function loginWithUsernamePassword(gameId, username, password) {
    const [user] = await directus.request(readItems('nhbgames_users_credentials', {
        filter: { username: { _eq: "samuel-api-3" }, game: { _eq: gameId } },
        limit: 1,
    }));
    if (!user) throw new Error("Invalid credentials");

    const isMatch = await directus.request(verifyHash(password, user.password));

    if (!isMatch) throw new Error("Invalid credentials");

    return { username, type: "username_password" };
}

// ✅ Login with mobile + otp
async function loginWithMobileOtp(gameId, mobile, otp) {
    // In real life: OTP should be verified via Redis/DB
    if (otp !== "1234") throw new Error("Invalid OTP");

    // Check if user exists in nhbgames_users_otp 
    const [user] = await directus.request(readItems('nhbgames_users_otp', {
        filter: {
            mobile_number: { _eq: mobile },
            games: {
                nhbgames_games_id: { _eq: gameId }
            }
        },
        limit: 1,
    }));
    if (!user) {
        await directus.request(createItem('nhbgames_users_otp', {
            mobile_number: mobile,
            games: [gameId],
        }));
    } else {
        await directus.request(updateItem('nhbgames_users_otp', user.id, {
            games: [...user.games, gameId],
        }));
    }

    return { mobile, type: "mobile_otp" };
}

// ✅ Login with heritage SSO
async function loginWithHeritageSSO(gameId, ssoToken) {
    // In real life: validate token with SSO provider
    if (ssoToken !== "valid_sso_token") throw new Error("Invalid SSO token");

    return { sso: true, type: "heritage_sso" };
}

// 🔑 JWT verifier
async function verifyRefreshToken(userId, loginType, token) {
    const [session] = await directus.request(readItems('nhbgames_user_sessions', {
        filter: { id: { _eq: userId }, type: { _eq: loginType } },
        limit: 1,
    }));
    if (!session) throw new Error("Invalid session");

    const result = await directus.request(verifyHash(token, session.refresh_token));
    if (!result) throw new Error("Invalid refresh token");

    return jwt.verify(token, REFRESH_SECRET_KEY);
}

// � JWT middleware for access token verification
function verifyAccessToken(req, res, next) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ message: "Access token required" });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
        const decoded = jwt.verify(token, SECRET_KEY);
        req.user = decoded;
        next();
    } catch (err) {
        return res.status(403).json({ message: "Invalid or expired access token" });
    }
}

// �🔄 Upsert refresh token
async function upsertTokens(id, type, refreshToken) {
    try {
        const [session] = await directus.request(readItems('nhbgames_user_sessions', {
            filter: { id: { _eq: id }, type: { _eq: type } },
            limit: 1,
        }));

        if (session) {
            await directus.request(updateItem('nhbgames_user_sessions', id, {
                refresh_token: refreshToken,
            }));
        } else {
            await directus.request(createItem('nhbgames_user_sessions', {
                id,
                type,
                refresh_token: refreshToken,
            }));
        }

        return true
    } catch (error) {
        console.error('Error upserting tokens:', error);
        return false
    }
}

// ❌ Invalidate refresh token
async function invalidateRefreshToken(userId) {
    try {
        await directus.request(deleteItem('nhbgames_user_sessions', userId));
        return true
    } catch (error) {
        console.error('Error invalidating refresh token:', error);
        return false
    }
}

app.post('/login', async (req, res) => {
    const { type, gameId } = req.body;

    try {
        let payload;

        switch (type) {
            case "username_password":
                payload = await loginWithUsernamePassword(gameId, req.body.username, req.body.password);
                break;
            case "mobile_otp":
                payload = await loginWithMobileOtp(gameId, req.body.mobile, req.body.otp);
                break;
            case "heritage_sso":
                payload = await loginWithHeritageSSO(gameId, req.body.token);
                break;
            default:
                return res.status(400).json({ message: "Unsupported login type" });
        }

        const accessToken = generateAccessToken(payload);
        const refreshToken = generateRefreshToken(payload);

        let upsertResult;
        switch (type) {
            case "username_password":
                upsertResult = await upsertTokens(req.body.username, type, refreshToken);
                break;
            case "mobile_otp":
                upsertResult = await upsertTokens(req.body.mobile, type, refreshToken);
                break;
            case "heritage_sso":
                upsertResult = await upsertTokens(req.body.email, type, refreshToken);
                break;
            default:
                return res.status(400).json({ message: "Unsupported login type" });
        }

        if (!upsertResult) {
            return res.status(500).json({ message: "Error upserting tokens" });
        }

        res.cookie("refreshToken", refreshToken, {
            httpOnly: true,
            secure: true,   // set true if HTTPS
            sameSite: "strict",
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        });

        return res.json({ accessToken });
    } catch (error) {
        console.error(error)
    }
})

app.post("/refresh-token", async (req, res) => {
    const { userId, loginType } = req.body

    const token = req.cookies.refreshToken;
    if (!token) return res.status(401).json({ message: "No refresh token" });

    try {
        const decoded = await verifyRefreshToken(userId, loginType, token);
        const { exp, iat, ...cleanPayload } = decoded;
        const newAccessToken = generateAccessToken(cleanPayload);
        return res.json({ accessToken: newAccessToken });
    } catch (err) {
        return res.status(403).json({ message: err.message });
    }
});

app.post("/logout", async (req, res) => {
    const token = req.cookies.refreshToken;
    if (token) {
        const decoded = jwt.verify(token, REFRESH_SECRET_KEY);

        const result = await invalidateRefreshToken(decoded.username);

        if (!result) {
            return res.status(500).json({ message: "Error invalidating token" });
        }
    }
    res.clearCookie("refreshToken");
    return res.json({ message: "Logged out" });
});

app.get('/asset/:id', async (req, res) => {
    const { id } = req.params;
    const assetUrl = `${DIRECTUS_URL}/assets/${id}?access_token=${DIRECTUS_TOKEN}`;

    try {
        const response = await fetch(assetUrl);
        if (!response.ok) {
            return res.status(response.status).send('Asset not found');
        }

        // Set headers from the original response
        res.set('Content-Type', response.headers.get('content-type'));

        // Convert web stream to buffer and send
        const buffer = Buffer.from(await response.arrayBuffer());
        res.send(buffer);
    } catch (err) {
        console.error('Error fetching asset:', err);
        res.status(500).send('Error fetching asset');
    }
});

function rewriteImageSrc(htmlString) {
    const dom = new JSDOM(htmlString);
    const { document } = dom.window;

    document.querySelectorAll("img").forEach(img => {
        const src = img.getAttribute("src");
        try {
            const url = new URL(src);
            if (url.hostname === "nhboptical.vtsuki.net") {
                const fileName = url.pathname.split("/").pop();
                const assetId = fileName.split(".")[0];
                img.setAttribute("src", `http://localhost:3000/asset/${assetId}`);
            }
        } catch (err) {
            console.error("Invalid image src:", src, err);
        }
    });

    return document.body.innerHTML;
}

app.get('/game/:url', async (req, res) => {
    try {
        const { url } = req.params;

        const [game] = await directus.request(readItems('nhbgames_games', {
            filter: { status: { _eq: "published" }, url: { _eq: url }, },
            fields: ["id", "name", "url", "start_date", "end_date", "login_type",
                { theme: ["variables_string", "variables_file", "labels"] },
                "logo", "footer", "show_NHB_mailing_list", "rewards_integration", "reward_method", "mode",
                { "pages": ["status", "show_in_menu", "menu_label", "pathname", "sort", "title", "content", "activation_timing", "type", "redirect"] },
                {
                    "task_categories":
                        ["status", "sort", "name",
                            {
                                "tasks": [
                                    "id",
                                    "order",
                                    "status",
                                    "game_type",
                                    { "qr_task": ["qr_payload"] },
                                    { "ar_task": ["provider", "url"] },
                                    { "map_task": ["geospatial", "radius", "clue"] },
                                    "points_awarded",
                                    "name",
                                    "title",
                                    { "carousel": ["sort", "directus_files_id"] },
                                    "contentBeforeTaskCompletion",
                                    "contentAfterTaskCompletion"
                                ]
                            }]
                },
                {
                    "rewards": [
                        "id",
                        "status",
                        "sort",
                        "name",
                        "description",
                        "thumbnail",
                        "reward_method",
                        "task_category",
                        "task",
                        "points_required",
                        "external_integration",
                        "integration_system",
                        "method",
                        "passcode",
                        "display",
                        "url_template",
                        "non_unique_voucher",
                        "expired_at",


                    ]
                }],
            limit: 1,
            backlink: true,
        }));

        if (!game) {
            return res.status(404).json({ message: "Game not found" });
        }

        const originalTheme = structuredClone ? structuredClone(game.theme) : JSON.parse(JSON.stringify(game.theme));
        const publishedPages = game.pages.filter(p => p.status === "published");


        // Transform logo
        game.logo = `http://localhost:3000/asset/${game.logo}`;

        // Transform footer
        game.footer = rewriteImageSrc(game.footer);

        // Transform pages menu
        game.menu = publishedPages.filter(p => p.show_in_menu).sort((p1, p2) => p1.sort - p2.sort).map(p => ({ label: p.menu_label, url: p.pathname }));

        // Transform theme
        game.theme = {
            styles: {
                ...Object.fromEntries(
                    originalTheme.variables_string.map(v => [v.name, v.value])
                ),
            },
            icons: {
                ...Object.fromEntries(
                    originalTheme.variables_file.map(v => [v.name, `http://localhost:3000/asset/${v.value.key}`])
                )
            },
            labels: {
                ...Object.fromEntries(
                    originalTheme.labels.map(l => [l.name, l.value])
                )
            },
        }

        // Transform task categories
        game.task_categories = game.task_categories.filter(c => c.status === "published");
        game.task_categories.forEach(c => {
            c.tasks = c.tasks.filter(t => t.status === "published")

            c.tasks.forEach(t => {
                if (!t.qr_task) delete t.qr_task;
                if (!t.ar_task) delete t.ar_task;
                if (!t.map_task) delete t.map_task;

                t.carousel.forEach(i => {
                    i.image = `http://localhost:3000/asset/${i.directus_files_id}`;
                    delete i.directus_files_id;
                });
            });
        });

        // Transform rewards
        if (game.rewards_integration) {
            game.rewards = game.rewards.filter(r => r.status === "published");
            game.rewards.forEach(r => {
                r.thumbnail = `http://localhost:3000/asset/${r.thumbnail}`;

                switch (r.reward_method) {
                    case "on-task-category-completion": {
                        delete r.task
                        delete r.points_required
                        break;
                    }
                    case "on-task-completion": {
                        delete r.task_category
                        delete r.points_required
                        break;
                    }
                    case "points-milestones": {
                        delete r.task_category
                        delete r.task
                        break;
                    }
                    default: {
                        break;
                    }
                }

                switch (r.external_integration) {
                    case true: {
                        delete r.passcode
                        delete r.method
                        delete r.non_unique_voucher
                        delete r.expired_at
                        delete r.display
                        delete r.url_template
                        break;
                    }
                    case false: {
                        delete r.integration_system
                        break;
                    }
                }

                switch (r.method) {
                    case "passcode": {
                        delete r.non_unique_voucher
                        delete r.expired_at
                        delete r.display
                        delete r.url_template
                        break;
                    }

                    case "vouchers": {
                        delete r.passcode
                        delete r.non_unique_voucher
                        delete r.expired_at
                        break;
                    }

                    case "non-unique-vouchers": {
                        delete r.passcode
                        r.url_template = r.url_template.replace(/{{code}}/g, r.non_unique_voucher);
                        break;
                    }

                    default: {
                        break;
                    }
                }

                switch (r.display) {
                    case "web-link": {
                        break;
                    }

                    default: {
                        delete r.url_template
                        break;
                    }
                }
            });
        } else {
            delete game.rewards;
        }

        return res.json({ game });
    } catch (error) {
        console.error(error)
    }
})

// Helper function to get player data based on login type
async function getPlayerData(user, gameId) {
    let player = null;

    switch (user.type) {
        case "username_password":
            [player] = await directus.request(readItems('nhbgames_users_credentials', {
                filter: { username: { _eq: user.username }, game: { _eq: gameId } },
                limit: 1,
            }));
            break;
        case "mobile_otp":
            [player] = await directus.request(readItems('nhbgames_users_otp', {
                filter: {
                    mobile_number: { _eq: user.mobile },
                    games: {
                        nhbgames_games_id: { _eq: gameId }
                    }
                },
                limit: 1,
            }));
            break;
        case "heritage_sso":
            // For SSO, we might need to create or find player based on email
            // This is a placeholder - actual implementation depends on SSO setup
            player = { id: user.email, type: "heritage_sso" };
            break;
    }

    return player;
}

// Helper function to calculate progress based on reward method
async function calculatePlayerProgress(playerId, game) {
    const progress = {
        totalPoints: 0,
        completedTasks: [],
        completedCategories: [],
        availableRewards: [],
        earnedRewards: []
    };

    try {
        // Get player task completions (assuming a table exists)
        const taskCompletions = await directus.request(readItems('nhbgames_player_task_completions', {
            filter: {
                player_id: { _eq: playerId },
                game_id: { _eq: game.id }
            }
        })).catch(() => []); // Fallback to empty array if table doesn't exist

        // Calculate total points and completed tasks
        taskCompletions.forEach(completion => {
            const task = game.task_categories
                .flatMap(cat => cat.tasks)
                .find(t => t.id === completion.task_id);

            if (task) {
                progress.totalPoints += task.points_awarded || 0;
                progress.completedTasks.push(completion.task_id);
            }
        });

        // Calculate completed categories
        game.task_categories.forEach(category => {
            const categoryTasks = category.tasks.map(t => t.id);
            const completedCategoryTasks = progress.completedTasks.filter(taskId =>
                categoryTasks.includes(taskId)
            );

            if (completedCategoryTasks.length === categoryTasks.length && categoryTasks.length > 0) {
                progress.completedCategories.push(category.name);
            }
        });

        // Calculate available and earned rewards based on reward method
        if (game.rewards_integration && game.rewards) {
            game.rewards.forEach(reward => {
                let isEarned = false;

                switch (reward.reward_method) {
                    case "points-milestones":
                        if (progress.totalPoints >= (reward.points_required || 0)) {
                            isEarned = true;
                        }
                        break;
                    case "on-task-category-completion":
                        if (reward.task_category && progress.completedCategories.includes(
                            game.task_categories.find(cat => cat.id === reward.task_category)?.name
                        )) {
                            isEarned = true;
                        }
                        break;
                    case "on-task-completion":
                        if (reward.task && progress.completedTasks.includes(reward.task)) {
                            isEarned = true;
                        }
                        break;
                }

                if (isEarned) {
                    progress.earnedRewards.push({
                        id: reward.id,
                        name: reward.name,
                        description: reward.description,
                        thumbnail: reward.thumbnail
                    });
                } else {
                    progress.availableRewards.push({
                        id: reward.id,
                        name: reward.name,
                        description: reward.description,
                        thumbnail: reward.thumbnail,
                        requirement: getRewardRequirement(reward, game)
                    });
                }
            });
        }

    } catch (error) {
        console.error('Error calculating player progress:', error);
    }

    return progress;
}

// Helper function to get reward requirement description
function getRewardRequirement(reward, game) {
    switch (reward.reward_method) {
        case "points-milestones":
            return `Earn ${reward.points_required} points`;
        case "on-task-category-completion": {
            const category = game.task_categories.find(cat => cat.id === reward.task_category);
            return `Complete all tasks in "${category?.name}" category`;
        }
        case "on-task-completion": {
            const task = game.task_categories
                .flatMap(cat => cat.tasks)
                .find(t => t.id === reward.task);
            return `Complete task "${task?.name}"`;
        }
        default:
            return "Unknown requirement";
    }
}

app.get('/player/progress', verifyAccessToken, async (req, res) => {
    try {
        const { gameId } = req.query;

        if (!gameId) {
            return res.status(400).json({ message: "gameId query parameter is required" });
        }

        // Get player data based on the authenticated user
        const player = await getPlayerData(req.user, gameId);
        if (!player) {
            return res.status(404).json({ message: "Player not found for this game" });
        }

        // Get game data to determine reward method
        const [game] = await directus.request(readItems('nhbgames_games', {
            filter: { id: { _eq: gameId }, status: { _eq: "published" } },
            fields: ["id", "name", "reward_method", "rewards_integration",
                {
                    "task_categories": [
                        "id", "name", "status",
                        {
                            "tasks": [
                                "id", "name", "points_awarded", "status"
                            ]
                        }
                    ]
                },
                {
                    "rewards": [
                        "id", "name", "description", "thumbnail", "reward_method",
                        "task_category", "task", "points_required", "status"
                    ]
                }
            ],
            limit: 1,
        }));

        if (!game) {
            return res.status(404).json({ message: "Game not found" });
        }

        // Filter published content
        game.task_categories = game.task_categories?.filter(c => c.status === "published") || [];
        game.task_categories.forEach(c => {
            c.tasks = c.tasks?.filter(t => t.status === "published") || [];
        });
        game.rewards = game.rewards?.filter(r => r.status === "published") || [];

        // Calculate player progress based on reward method
        const playerId = player.id || player.username || player.mobile_number || player.email;
        const progress = await calculatePlayerProgress(playerId, game);

        return res.json({
            progress: {
                ...progress,
                gameId: game.id,
                gameName: game.name,
                rewardMethod: game.reward_method
            }
        });

    } catch (error) {
        console.error('Error fetching player progress:', error);
        return res.status(500).json({ message: "Internal server error" });
    }
});

app.listen(3000, () => {
    directus = createDirectus(DIRECTUS_URL)
        .with(staticToken(DIRECTUS_TOKEN))
        .with(rest());
    console.log(`Directus created`)
})
