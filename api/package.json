{"name": "api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@directus/sdk": "^20.0.2", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^5.1.0", "install": "^0.13.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "nodemon": "^3.1.10", "npm": "^11.5.2"}}