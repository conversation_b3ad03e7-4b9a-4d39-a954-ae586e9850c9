import { getGameContext, getAuthContext } from '$lib/context/sdk-context';

/**
 * Composable hook to access game data using SDK
 */
export function useGame() {
	const gameStore = getGameContext();
	
	return {
		// Direct access to reactive state
		get data() { return gameStore.state.data; },
		get loading() { return gameStore.state.loading; },
		get error() { return gameStore.state.error; },
		get isReady() { return gameStore.isReady; },
		
		// Methods
		loadGame: gameStore.loadGame.bind(gameStore),
		reset: gameStore.reset.bind(gameStore),
		setAutoRefreshInterval: gameStore.setAutoRefreshInterval.bind(gameStore),
		disableAutoRefresh: gameStore.disableAutoRefresh.bind(gameStore),
		enableAutoRefresh: gameStore.enableAutoRefresh.bind(gameStore),
		
		// Current auto-refresh interval
		get autoRefreshInterval() {
			return gameStore.autoRefreshInterval;
		}
	};
}

/**
 * Composable hook to access authentication using SDK
 */
export function useAuth() {
	const authStore = getAuthContext();
	
	return {
		// Direct access to reactive state
		get user() { return authStore.state.user; },
		get accessToken() { return authStore.state.accessToken; },
		get isAuthenticated() { return authStore.state.isAuthenticated; },
		get loading() { return authStore.state.loading; },
		get error() { return authStore.state.error; },
		
		// Methods
		login: authStore.login.bind(authStore),
		logout: authStore.logout.bind(authStore),
		clearError: authStore.clearError.bind(authStore),
		
		// Helper methods for different login types
		loginWithUsernamePassword: (username: string, password: string) => {
			return authStore.login({
				type: 'username_password',
				username,
				password
			});
		},
		
		loginWithMobileOtp: (mobile: string, otp: string) => {
			return authStore.login({
				type: 'mobile_otp',
				mobile,
				otp
			});
		},
		
		loginWithHeritageSSO: (token: string, email: string) => {
			return authStore.login({
				type: 'heritage_sso',
				token,
				email
			});
		}
	};
}
