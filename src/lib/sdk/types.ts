import { z } from 'zod';

// ===== AUTH TYPES =====
export const LoginTypeSchema = z.union([
	z.literal('username_password'),
	z.literal('mobile_otp'),
	z.literal('heritage_sso')
]);

export const LoginCredentialsSchema = z.object({
	type: LoginTypeSchema,
	gameId: z.string(),
	// For username_password
	username: z.string().optional(),
	password: z.string().optional(),
	// For mobile_otp
	mobile: z.string().optional(),
	otp: z.string().optional(),
	// For heritage_sso
	token: z.string().optional(),
	email: z.string().optional()
});

export const AuthUserSchema = z.object({
	username: z.string().optional(),
	mobile: z.string().optional(),
	email: z.string().optional(),
	sso: z.boolean().optional(),
	type: LoginTypeSchema
});

export const AuthTokensSchema = z.object({
	accessToken: z.string(),
	refreshToken: z.string().optional()
});

export const RefreshTokenRequestSchema = z.object({
	userId: z.string(),
	loginType: LoginTypeSchema
});

export const AccessTokenSchema = z.object({
	type: LoginTypeSchema,
	username: z.string().optional(),
	mobile: z.string().optional(),
	email: z.string().optional(),
	sso: z.boolean().optional(),
	iat: z.number(),
	exp: z.number()
});

// API Response schemas
export const LoginResponseSchema = z.object({
	accessToken: z.string()
});

export const RefreshTokenResponseSchema = z.object({
	accessToken: z.string()
});

export const LogoutResponseSchema = z.object({
	message: z.string()
});

export const GamesListResponseSchema = z.object({
	games: z.array(z.unknown()).optional()
});

// ===== GAME TYPES =====
export const GameThemeSchema = z.object({
	styles: z.record(z.string(), z.string()),
	labels: z.record(z.string(), z.string())
});

export const GamePageSchema = z.object({
	status: z.union([z.literal('published'), z.literal('draft')]),
	show_in_menu: z.boolean(),
	menu_label: z.string().nullable(),
	pathname: z.string(),
	sort: z.number(),
	title: z.string(),
	content: z.string().nullish(),
	activation_timing: z.array(
		z.union([z.literal('before_game'), z.literal('during_game'), z.literal('after_game')])
	),
	type: z.union([
		z.literal('static'),
		z.literal('pre-game'),
		z.literal('post-game'),
		z.literal('landing-page'),
		z.literal('game-completion')
	]),
	redirect: z.boolean().nullish()
});

export const TaskCarouselSchema = z.object({
	sort: z.number(),
	image: z.string()
});

export const MapTaskSchema = z.object({
	geospatial: z.object({
		type: z.literal('Point'),
		coordinates: z.tuple([z.number(), z.number()])
	}),
	radius: z.number(),
	clue: z.string()
});

export const ArTaskSchema = z.object({
	provider: z.string(),
	url: z.string()
});

export const GameTaskSchema = z.object({
	id: z.string().uuid(),
	order: z.number(),
	status: z.union([z.literal('published'), z.literal('draft')]),
	game_type: z.string(),
	points_awarded: z.number(),
	name: z.string(),
	title: z.string().nullish(),
	contentBeforeTaskCompletion: z.string().nullable(),
	contentAfterTaskCompletion: z.string().nullable(),
	map_task: MapTaskSchema.optional(),
	ar_task: ArTaskSchema.optional(),
	carousel: z.array(TaskCarouselSchema)
});

export const TaskCategorySchema = z.object({
	status: z.union([z.literal('published'), z.literal('draft')]),
	sort: z.number(),
	name: z.string(),
	tasks: z.array(GameTaskSchema)
});

export const RewardSchema = z.object({
	id: z.string().uuid(),
	status: z.union([z.literal('published'), z.literal('draft')]),
	sort: z.number(),
	name: z.string(),
	description: z.string().nullish(),
	thumbnail: z.string(),
	reward_method: z.union([
		z.literal('on-task-category-completion'),
		z.literal('on-task-completion'),
		z.literal('points-milestones')
	]),
	task_category: z.string().uuid().optional(),
	task: z.string().uuid().optional(),
	external_integration: z.boolean(),
	method: z.string().nullish(),
	passcode: z.string().optional(),
	display: z.string().optional(),
	points_required: z.number().optional(),
	non_unique_voucher: z.string().optional(),
	expired_at: z.string().optional()
});

export const GameMenuSchema = z.object({
	label: z.string(),
	url: z.string()
});

export const GameSchema = z.object({
	id: z.string().uuid(),
	name: z.string(),
	url: z.string(),
	start_date: z.string(), // ISO string
	end_date: z.string(), // ISO string
	login_type: z.string(),
	logo: z.string(),
	footer: z.string(),
	show_NHB_mailing_list: z.boolean(),
	rewards_integration: z.boolean(),
	theme: GameThemeSchema,
	pages: z.array(GamePageSchema),
	task_categories: z.array(TaskCategorySchema),
	rewards: z.array(RewardSchema),
	menu: z.array(GameMenuSchema)
});

export const GameResponseSchema = z.object({
	game: GameSchema
});

// ===== TYPESCRIPT TYPES =====
export type LoginType = z.infer<typeof LoginTypeSchema>;
export type LoginCredentials = z.infer<typeof LoginCredentialsSchema>;
export type AuthUser = z.infer<typeof AuthUserSchema>;
export type AuthTokens = z.infer<typeof AuthTokensSchema>;
export type RefreshTokenRequest = z.infer<typeof RefreshTokenRequestSchema>;
export type AccessToken = z.infer<typeof AccessTokenSchema>;
export type LoginResponse = z.infer<typeof LoginResponseSchema>;
export type RefreshTokenResponse = z.infer<typeof RefreshTokenResponseSchema>;
export type LogoutResponse = z.infer<typeof LogoutResponseSchema>;
export type GamesListResponse = z.infer<typeof GamesListResponseSchema>;

export type GameTheme = z.infer<typeof GameThemeSchema>;
export type GamePage = z.infer<typeof GamePageSchema>;
export type TaskCarousel = z.infer<typeof TaskCarouselSchema>;
export type MapTask = z.infer<typeof MapTaskSchema>;
export type ArTask = z.infer<typeof ArTaskSchema>;
export type GameTask = z.infer<typeof GameTaskSchema>;
export type TaskCategory = z.infer<typeof TaskCategorySchema>;
export type Reward = z.infer<typeof RewardSchema>;
export type GameMenu = z.infer<typeof GameMenuSchema>;
export type Game = z.infer<typeof GameSchema>;
export type GameResponse = z.infer<typeof GameResponseSchema>;
