import { HttpClient } from './http-client';
import { ErrorFactory, ValidationError } from './errors';
import {
	AuthTokensSchema,
	AccessTokenSchema,
	LoginResponseSchema,
	RefreshTokenResponseSchema,
	LogoutResponseSchema,
	type LoginCredentials,
	type AuthTokens,
	type RefreshTokenRequest,
	type AccessToken,
	type LoginResponse,
	type RefreshTokenResponse,
	type LogoutResponse
} from './types';

export class AuthAPI {
	private httpClient = new HttpClient();

	/**
	 * Login with different authentication methods
	 */
	async login(credentials: LoginCredentials): Promise<AuthTokens> {
		try {
			const data = await this.httpClient.post<LoginResponse>('/login', credentials, {
				credentials: 'include' // Required for cookies (refresh token)
			});

			// Validate response with Zod schema
			const validatedResponse = LoginResponseSchema.parse(data);
			const validatedTokens = AuthTokensSchema.parse({
				accessToken: validatedResponse.accessToken
			});

			return validatedTokens;
		} catch (error) {
			if (error instanceof Error && error.message.includes('parse')) {
				throw ErrorFactory.createFromZodError(error);
			}
			throw error;
		}
	}

	/**
	 * Refresh access token using refresh token from cookie
	 */
	async refreshToken(request: RefreshTokenRequest): Promise<AuthTokens> {
		const data = await this.httpClient.post<RefreshTokenResponse>('/refresh-token', request, {
			credentials: 'include' // Required for cookies (refresh token)
		});

		// Validate response with Zod schema
		const validatedResponse = RefreshTokenResponseSchema.parse(data);
		const validatedTokens = AuthTokensSchema.parse({
			accessToken: validatedResponse.accessToken
		});

		return validatedTokens;
	}

	/**
	 * Logout and invalidate refresh token
	 */
	async logout(): Promise<void> {
		const data = await this.httpClient.post<LogoutResponse>('/logout', undefined, {
			credentials: 'include' // Required for cookies (refresh token)
		});

		// Validate response (optional, but ensures API contract)
		LogoutResponseSchema.parse(data);
	}

	/**
	 * Decode JWT token to get user info (client-side only, for display purposes)
	 */
	decodeToken(token: string): AccessToken | null {
		try {
			const base64Url = token.split('.')[1];
			if (!base64Url) {
				throw new ValidationError('Invalid token format: missing payload');
			}

			const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
			const jsonPayload = decodeURIComponent(
				atob(base64)
					.split('')
					.map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
					.join('')
			);

			const parsed = JSON.parse(jsonPayload);
			return AccessTokenSchema.parse(parsed);
		} catch (error) {
			console.error('Error decoding token:', error);
			return null;
		}
	}

	/**
	 * Check if token is expired
	 */
	isTokenExpired(token: string): boolean {
		const decoded = this.decodeToken(token);
		if (!decoded || !decoded.exp) return true;

		const currentTime = Date.now() / 1000;
		return decoded.exp < currentTime;
	}

	/**
	 * Helper methods for different login types
	 */
	async loginWithUsernamePassword(gameId: string, username: string, password: string): Promise<AuthTokens> {
		return this.login({
			type: 'username_password',
			gameId,
			username,
			password
		});
	}

	async loginWithMobileOtp(gameId: string, mobile: string, otp: string): Promise<AuthTokens> {
		return this.login({
			type: 'mobile_otp',
			gameId,
			mobile,
			otp
		});
	}

	async loginWithHeritageSSO(gameId: string, token: string, email: string): Promise<AuthTokens> {
		return this.login({
			type: 'heritage_sso',
			gameId,
			token,
			email
		});
	}
}
