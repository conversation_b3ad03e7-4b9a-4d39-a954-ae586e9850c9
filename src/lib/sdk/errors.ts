/**
 * Base SDK Error class
 */
export class SDKError extends Error {
	public readonly code: string;
	public readonly statusCode?: number;
	public readonly originalError?: unknown;

	constructor(
		message: string,
		code: string,
		statusCode?: number,
		originalError?: unknown
	) {
		super(message);
		this.name = 'SDKError';
		this.code = code;
		this.statusCode = statusCode;
		this.originalError = originalError;

		// Maintains proper stack trace for where our error was thrown (only available on V8)
		if (Error.captureStackTrace) {
			Error.captureStackTrace(this, SDKError);
		}
	}
}

/**
 * Network-related errors
 */
export class NetworkError extends SDKError {
	constructor(message: string, statusCode?: number, originalError?: unknown) {
		super(message, 'NETWORK_ERROR', statusCode, originalError);
		this.name = 'NetworkError';
	}
}

/**
 * Authentication-related errors
 */
export class AuthenticationError extends SDKError {
	constructor(message: string, statusCode?: number, originalError?: unknown) {
		super(message, 'AUTHENTICATION_ERROR', statusCode, originalError);
		this.name = 'AuthenticationError';
	}
}

/**
 * Validation errors (Zod schema validation failures)
 */
export class ValidationError extends SDKError {
	public readonly validationDetails?: unknown;

	constructor(message: string, validationDetails?: unknown, originalError?: unknown) {
		super(message, 'VALIDATION_ERROR', undefined, originalError);
		this.name = 'ValidationError';
		this.validationDetails = validationDetails;
	}
}

/**
 * Configuration errors
 */
export class ConfigurationError extends SDKError {
	constructor(message: string, originalError?: unknown) {
		super(message, 'CONFIGURATION_ERROR', undefined, originalError);
		this.name = 'ConfigurationError';
	}
}

/**
 * Timeout errors
 */
export class TimeoutError extends SDKError {
	constructor(message: string = 'Request timeout', originalError?: unknown) {
		super(message, 'TIMEOUT_ERROR', undefined, originalError);
		this.name = 'TimeoutError';
	}
}

/**
 * Game not found errors
 */
export class GameNotFoundError extends SDKError {
	constructor(gameUrl: string, originalError?: unknown) {
		super(`Game not found: ${gameUrl}`, 'GAME_NOT_FOUND', 404, originalError);
		this.name = 'GameNotFoundError';
	}
}

/**
 * Token expired errors
 */
export class TokenExpiredError extends SDKError {
	constructor(message: string = 'Access token has expired', originalError?: unknown) {
		super(message, 'TOKEN_EXPIRED', 401, originalError);
		this.name = 'TokenExpiredError';
	}
}

/**
 * Error factory for creating appropriate error types based on HTTP status codes
 */
export class ErrorFactory {
	static createFromResponse(
		message: string,
		statusCode: number,
		originalError?: unknown
	): SDKError {
		switch (statusCode) {
			case 401:
				return new AuthenticationError(message, statusCode, originalError);
			case 404:
				return new GameNotFoundError(message, originalError);
			case 408:
			case 504:
				return new TimeoutError(message, originalError);
			default:
				if (statusCode >= 400 && statusCode < 500) {
					return new ValidationError(message, undefined, originalError);
				} else if (statusCode >= 500) {
					return new NetworkError(message, statusCode, originalError);
				} else {
					return new SDKError(message, 'UNKNOWN_ERROR', statusCode, originalError);
				}
		}
	}

	static createFromZodError(zodError: unknown): ValidationError {
		return new ValidationError(
			'Response validation failed',
			zodError,
			zodError
		);
	}

	static createNetworkError(message: string, originalError?: unknown): NetworkError {
		return new NetworkError(message, undefined, originalError);
	}

	static createTimeoutError(originalError?: unknown): TimeoutError {
		return new TimeoutError('Request timeout', originalError);
	}
}

/**
 * Type guard functions for error handling
 */
export function isSDKError(error: unknown): error is SDKError {
	return error instanceof SDKError;
}

export function isNetworkError(error: unknown): error is NetworkError {
	return error instanceof NetworkError;
}

export function isAuthenticationError(error: unknown): error is AuthenticationError {
	return error instanceof AuthenticationError;
}

export function isValidationError(error: unknown): error is ValidationError {
	return error instanceof ValidationError;
}

export function isTimeoutError(error: unknown): error is TimeoutError {
	return error instanceof TimeoutError;
}

export function isGameNotFoundError(error: unknown): error is GameNotFoundError {
	return error instanceof GameNotFoundError;
}

export function isTokenExpiredError(error: unknown): error is TokenExpiredError {
	return error instanceof TokenExpiredError;
}
