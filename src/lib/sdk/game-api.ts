import { HttpClient } from './http-client';
import { SDKConfigManager } from './config';
import { ErrorFactory, GameNotFoundError } from './errors';
import {
	GameResponseSchema,
	GamesListResponseSchema,
	type GameResponse,
	type GamesListResponse
} from './types';

export class GameAPI {
	private httpClient = new HttpClient();

	/**
	 * Fetch game data by URL
	 */
	async getGame(gameUrl: string): Promise<GameResponse | null> {
		try {
			const data = await this.httpClient.get<unknown>(`/game/${gameUrl}`);

			// Validate response with Zod schema
			const validatedData = GameResponseSchema.parse(data);

			return validatedData;
		} catch (error) {
			console.error('GameAPI.getGame error:', error);

			// Convert to appropriate error type
			if (error instanceof Error && error.message.includes('404')) {
				throw new GameNotFoundError(gameUrl, error);
			}

			if (error instanceof Error && error.message.includes('parse')) {
				throw ErrorFactory.createFromZodError(error);
			}

			// For now, return null for backward compatibility
			// In a breaking change, we could throw the error instead
			return null;
		}
	}

	/**
	 * Check if game exists (lightweight check)
	 */
	async gameExists(gameUrl: string): Promise<boolean> {
		try {
			await this.httpClient.head(`/game/${gameUrl}`);
			return true;
		} catch (error) {
			console.error('GameAPI.gameExists error:', error);
			return false;
		}
	}

	/**
	 * Get game asset URL
	 */
	getAssetUrl(assetId: string): string {
		// Get config from the singleton
		const config = SDKConfigManager.getInstance().getConfig();
		return `${config.apiUrl}/asset/${assetId}`;
	}

	/**
	 * Validate game data structure
	 */
	validateGameData(data: unknown): GameResponse | null {
		try {
			return GameResponseSchema.parse(data);
		} catch (error) {
			console.error('GameAPI.validateGameData error:', error);
			return null;
		}
	}

	/**
	 * Get multiple games (if API supports it)
	 */
	async getGames(filters?: { status?: 'published' | 'draft'; limit?: number }): Promise<GameResponse[]> {
		try {
			const queryParams = new URLSearchParams();
			if (filters?.status) queryParams.append('status', filters.status);
			if (filters?.limit) queryParams.append('limit', filters.limit.toString());

			const endpoint = `/games${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
			const data = await this.httpClient.get<GamesListResponse>(endpoint);

			// Validate response structure first
			const validatedResponse = GamesListResponseSchema.parse(data);

			// Validate each game response
			const validatedGames: GameResponse[] = [];
			for (const gameData of validatedResponse.games || []) {
				const validated = GameResponseSchema.safeParse({ game: gameData });
				if (validated.success) {
					validatedGames.push(validated.data);
				}
			}

			return validatedGames;
		} catch (error) {
			console.error('GameAPI.getGames error:', error);
			return [];
		}
	}
}
