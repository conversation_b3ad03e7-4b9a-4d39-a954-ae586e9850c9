export interface SDKConfig {
	apiUrl: string;
	timeout?: number;
	retries?: number;
}

export class SDKConfigManager {
	private static instance: SDKConfigManager;
	private config: SDKConfig;

	private constructor(config: SDKConfig) {
		this.config = {
			timeout: 10000, // 10 seconds default
			retries: 3, // 3 retries default
			...config
		};
	}

	public static getInstance(config?: SDKConfig): SDKConfigManager {
		if (!SDKConfigManager.instance) {
			if (!config) {
				throw new Error('SDK must be initialized with config first');
			}
			SDKConfigManager.instance = new SDKConfigManager(config);
		}
		return SDKConfigManager.instance;
	}

	public getConfig(): SDKConfig {
		return { ...this.config };
	}

	public updateConfig(newConfig: Partial<SDKConfig>): void {
		this.config = { ...this.config, ...newConfig };
	}
}
