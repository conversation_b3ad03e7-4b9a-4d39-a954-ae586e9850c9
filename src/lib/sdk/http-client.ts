import { SDKConfigManager } from './config';
import { ErrorFactory, TimeoutError, SDKError } from './errors';

export interface RequestOptions {
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'HEAD';
	headers?: Record<string, string>;
	body?: unknown;
	credentials?: 'include' | 'omit' | 'same-origin';
	timeout?: number;
}

export class HttpClient {
	private config = SDKConfigManager.getInstance().getConfig();

	async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
		const {
			method = 'GET',
			headers = {},
			body,
			credentials,
			timeout = this.config.timeout
		} = options;

		const url = `${this.config.apiUrl}${endpoint}`;
		
		const fetchOptions: RequestInit = {
			method,
			headers: {
				'Content-Type': 'application/json',
				...headers
			},
			...(credentials && { credentials })
		};

		if (body !== undefined) {
			fetchOptions.body = JSON.stringify(body);
		}

		// Create timeout promise
		const timeoutPromise = new Promise<never>((_, reject) => {
			setTimeout(() => reject(new TimeoutError()), timeout);
		});

		try {
			const response = await Promise.race([
				fetch(url, fetchOptions),
				timeoutPromise
			]);

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({ message: response.statusText }));
				const errorMessage = (errorData as { message?: string }).message || `HTTP ${response.status}: ${response.statusText}`;
				throw ErrorFactory.createFromResponse(errorMessage, response.status);
			}

			// Handle empty responses
			const contentType = response.headers.get('content-type');
			if (contentType && contentType.includes('application/json')) {
				return await response.json();
			} else {
				return {} as T;
			}
		} catch (error) {
			// Re-throw SDK errors as-is
			if (error instanceof SDKError) {
				throw error;
			}

			// Handle other errors
			if (error instanceof Error) {
				throw ErrorFactory.createNetworkError(error.message, error);
			}

			throw ErrorFactory.createNetworkError('Unknown error occurred', error);
		}
	}

	async get<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
		return this.request<T>(endpoint, { ...options, method: 'GET' });
	}

	async post<T>(endpoint: string, body?: unknown, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
		return this.request<T>(endpoint, { ...options, method: 'POST', body });
	}

	async put<T>(endpoint: string, body?: unknown, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
		return this.request<T>(endpoint, { ...options, method: 'PUT', body });
	}

	async delete<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
		return this.request<T>(endpoint, { ...options, method: 'DELETE' });
	}

	async head<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
		return this.request<T>(endpoint, { ...options, method: 'HEAD' });
	}
}
