import { SDKConfigManager, type SDKConfig } from './config';
import { AuthAPI } from './auth-api';
import { GameAPI } from './game-api';

// Re-export types for convenience
export * from './types';
export * from './errors';
export type { SDKConfig } from './config';

/**
 * Main SDK class that provides access to all APIs
 */
export class NHBGameSDK {
	public auth: AuthAPI;
	public game: GameAPI;

	constructor(config: SDKConfig) {
		// Initialize SDK configuration
		SDKConfigManager.getInstance(config);

		// Initialize API clients
		this.auth = new AuthAPI();
		this.game = new GameAPI();
	}

	/**
	 * Update SDK configuration
	 */
	updateConfig(newConfig: Partial<SDKConfig>): void {
		SDKConfigManager.getInstance().updateConfig(newConfig);
	}

	/**
	 * Get current SDK configuration
	 */
	getConfig(): SDKConfig {
		return SDKConfigManager.getInstance().getConfig();
	}

	/**
	 * Create a new SDK instance with different configuration
	 */
	static create(config: SDKConfig): NHBGameSDK {
		return new NHBGameSDK(config);
	}
}

/**
 * Default export for convenience
 */
export default NHBGameSDK;

/**
 * Factory function for creating SDK instances
 */
export function createSDK(config: SDKConfig): NHBGameSDK {
	return new NHBGameSDK(config);
}

/**
 * Standalone API classes for advanced usage
 */
export { AuthAPI } from './auth-api';
export { GameAPI } from './game-api';
export { HttpClient } from './http-client';
export { SDKConfigManager } from './config';
