# NHB Game SDK

A standalone TypeScript/JavaScript SDK for interacting with the NHB Game API. This SDK is framework-agnostic and can be used in any JavaScript environment (React, Vue, Angular, Node.js, etc.).

## Features

- 🔐 **Authentication**: Login, logout, token refresh with multiple auth types
- 🎮 **Game Management**: Fetch game data, validate games, asset management
- 🛡️ **Type Safety**: Full TypeScript support with Zod validation
- 🌐 **Framework Agnostic**: Works with any JavaScript framework or vanilla JS
- ⚡ **Modern**: Uses fetch API with timeout and retry support
- 📦 **Lightweight**: Minimal dependencies (only Zod for validation)

## Installation

```bash
# If using in the same project
import { NHBGameSDK } from '$lib/sdk';

# If extracted as separate package
npm install nhb-game-sdk
```

## Quick Start

```typescript
import { NHBGameSDK } from '$lib/sdk';

// Initialize SDK
const sdk = new NHBGameSDK({
  apiUrl: 'http://localhost:3000',
  timeout: 10000, // 10 seconds
  retries: 3
});

// Load game data
const gameData = await sdk.game.getGame('your-game-url');

// Authenticate user
const tokens = await sdk.auth.loginWithUsernamePassword(
  gameData.game.id,
  'username',
  'password'
);
```

## API Reference

### Configuration

```typescript
interface SDKConfig {
  apiUrl: string;        // Base API URL
  timeout?: number;      // Request timeout in ms (default: 10000)
  retries?: number;      // Number of retries (default: 3)
}
```

### Game API

```typescript
// Get game by URL
const game = await sdk.game.getGame('game-url');

// Check if game exists
const exists = await sdk.game.gameExists('game-url');

// Get asset URL
const assetUrl = sdk.game.getAssetUrl('asset-id');

// Get multiple games (if supported)
const games = await sdk.game.getGames({ status: 'published', limit: 10 });
```

### Authentication API

```typescript
// Username/Password login
const tokens = await sdk.auth.loginWithUsernamePassword(gameId, username, password);

// Mobile/OTP login
const tokens = await sdk.auth.loginWithMobileOtp(gameId, mobile, otp);

// Heritage SSO login
const tokens = await sdk.auth.loginWithHeritageSSO(gameId, token, email);

// Generic login
const tokens = await sdk.auth.login({
  type: 'username_password',
  gameId: 'game-id',
  username: 'user',
  password: 'pass'
});

// Refresh token
const newTokens = await sdk.auth.refreshToken({ userId, loginType });

// Logout
await sdk.auth.logout();

// Token utilities
const decoded = sdk.auth.decodeToken(accessToken);
const isExpired = sdk.auth.isTokenExpired(accessToken);
```

## Usage Examples

### React Example

```typescript
import { useEffect, useState } from 'react';
import { NHBGameSDK, type Game } from '$lib/sdk';

const sdk = new NHBGameSDK({ apiUrl: process.env.REACT_APP_API_URL });

function GameComponent() {
  const [game, setGame] = useState<Game | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadGame() {
      const response = await sdk.game.getGame('my-game');
      if (response) {
        setGame(response.game);
      }
      setLoading(false);
    }
    loadGame();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (!game) return <div>Game not found</div>;

  return <div>Welcome to {game.name}!</div>;
}
```

### Node.js Example

```typescript
import { NHBGameSDK } from './sdk';

const sdk = new NHBGameSDK({
  apiUrl: 'http://localhost:3000'
});

async function main() {
  // Load game
  const gameResponse = await sdk.game.getGame('test-game');
  if (!gameResponse) {
    console.error('Game not found');
    return;
  }

  // Authenticate
  try {
    const tokens = await sdk.auth.loginWithUsernamePassword(
      gameResponse.game.id,
      'samuel-api-3',
      'password'
    );
    console.log('Login successful!', tokens);
  } catch (error) {
    console.error('Login failed:', error);
  }
}

main();
```

### Vue Example

```vue
<template>
  <div>
    <div v-if="loading">Loading...</div>
    <div v-else-if="game">
      <h1>{{ game.name }}</h1>
      <button @click="login" :disabled="authenticating">
        {{ authenticating ? 'Logging in...' : 'Login' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NHBGameSDK, type Game } from '$lib/sdk';

const sdk = new NHBGameSDK({ apiUrl: import.meta.env.VITE_API_URL });

const game = ref<Game | null>(null);
const loading = ref(true);
const authenticating = ref(false);

onMounted(async () => {
  const response = await sdk.game.getGame('my-game');
  if (response) {
    game.value = response.game;
  }
  loading.value = false;
});

async function login() {
  if (!game.value) return;
  
  authenticating.value = true;
  try {
    await sdk.auth.loginWithUsernamePassword(
      game.value.id,
      'username',
      'password'
    );
    console.log('Login successful!');
  } catch (error) {
    console.error('Login failed:', error);
  } finally {
    authenticating.value = false;
  }
}
</script>
```

## Error Handling

The SDK provides comprehensive, type-safe error handling with specific error types:

```typescript
import {
  AuthenticationError,
  NetworkError,
  TimeoutError,
  ValidationError,
  GameNotFoundError,
  isAuthenticationError,
  isTimeoutError
} from '$lib/sdk';

try {
  const tokens = await sdk.auth.login(credentials);
} catch (error) {
  if (isAuthenticationError(error)) {
    console.error('Authentication failed:', error.message);
    // Handle authentication error
  } else if (isTimeoutError(error)) {
    console.error('Request timed out:', error.message);
    // Handle timeout error
  } else if (error instanceof GameNotFoundError) {
    console.error('Game not found:', error.message);
    // Handle game not found
  } else if (error instanceof ValidationError) {
    console.error('Invalid response format:', error.validationDetails);
    // Handle validation error
  } else {
    console.error('Unknown error:', error);
    // Handle other errors
  }
}
```

### Available Error Types

- `SDKError` - Base error class
- `NetworkError` - Network-related errors (5xx status codes)
- `AuthenticationError` - Authentication failures (401 status codes)
- `ValidationError` - Response validation failures (Zod schema errors)
- `TimeoutError` - Request timeout errors
- `GameNotFoundError` - Game not found errors (404 for games)
- `TokenExpiredError` - Expired access token errors
- `ConfigurationError` - SDK configuration errors

### Type Guards

Use type guard functions for safe error handling:

```typescript
import {
  isSDKError,
  isNetworkError,
  isAuthenticationError,
  isValidationError,
  isTimeoutError,
  isGameNotFoundError,
  isTokenExpiredError
} from '$lib/sdk';

if (isSDKError(error)) {
  console.log('SDK Error Code:', error.code);
  console.log('Status Code:', error.statusCode);
}
```

## TypeScript Support

The SDK is fully typed with TypeScript and includes Zod validation for runtime type safety:

```typescript
import type { 
  Game, 
  AuthTokens, 
  LoginCredentials,
  GameResponse 
} from '$lib/sdk';

// All types are exported and available
const credentials: LoginCredentials = {
  type: 'username_password',
  gameId: 'game-id',
  username: 'user',
  password: 'pass'
};
```

## Advanced Usage

### Custom HTTP Client

```typescript
import { HttpClient } from '$lib/sdk';

const httpClient = new HttpClient();
const customData = await httpClient.post('/custom-endpoint', { data: 'value' });
```

### Standalone API Classes

```typescript
import { AuthAPI, GameAPI } from '$lib/sdk';

// Use individual API classes
const authAPI = new AuthAPI();
const gameAPI = new GameAPI();
```

This SDK provides a clean, type-safe interface to the NHB Game API that can be used across different projects and frameworks!
