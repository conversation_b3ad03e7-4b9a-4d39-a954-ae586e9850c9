import { NHBGameSDK, type Game, type AuthUser, type GameResponse } from '$lib/sdk';
import { PUBLIC_API_URL } from '$env/static/public';

// Initialize SDK
const sdk = new NHBGameSDK({
	apiUrl: PUBLIC_API_URL,
	timeout: 10000,
	retries: 3
});

// Game Store using SDK
export class SDKGameStore {
	public state = $state<{
		data: Game | null;
		loading: boolean;
		error: string | null;
	}>({
		data: null,
		loading: false,
		error: null
	});

	private refreshInterval: number | null = null;
	private currentGameUrl: string | null = null;
	public autoRefreshInterval = 60000; // 1 minute

	// Getters
	get data() {
		return this.state.data;
	}

	get loading() {
		return this.state.loading;
	}

	get error() {
		return this.state.error;
	}

	get isReady() {
		return !this.state.loading && this.state.data !== null && this.state.error === null;
	}

	async loadGame(gameUrl: string, enableAutoRefresh: boolean = true) {
		if (this.state.loading) return;

		this.currentGameUrl = gameUrl;
		this.stopAutoRefresh();

		this.state.loading = true;
		this.state.error = null;

		try {
			const response: GameResponse | null = await sdk.game.getGame(gameUrl);
			if (response?.game) {
				this.state.data = response.game;
				
				if (enableAutoRefresh && this.autoRefreshInterval > 0) {
					this.startAutoRefresh();
				}
			} else {
				this.state.error = 'Game not found';
			}
		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'Failed to load game';
		} finally {
			this.state.loading = false;
		}
	}

	private startAutoRefresh() {
		if (this.refreshInterval) return;
		
		this.refreshInterval = window.setInterval(
			async () => {
				if (this.currentGameUrl && !this.state.loading) {
					try {
						const response: GameResponse | null = await sdk.game.getGame(this.currentGameUrl);
						if (response?.game) {
							this.state.data = response.game;
							this.state.error = null;
						}
					} catch (error) {
						console.warn('Auto-refresh failed:', error);
					}
				}
			},
			this.autoRefreshInterval
		);
	}

	private stopAutoRefresh() {
		if (this.refreshInterval) {
			clearInterval(this.refreshInterval);
			this.refreshInterval = null;
		}
	}

	reset() {
		this.stopAutoRefresh();
		this.currentGameUrl = null;
		this.state.data = null;
		this.state.loading = false;
		this.state.error = null;
	}

	setAutoRefreshInterval(milliseconds: number) {
		this.autoRefreshInterval = milliseconds;
		
		if (this.refreshInterval && this.currentGameUrl) {
			this.stopAutoRefresh();
			this.startAutoRefresh();
		}
	}

	disableAutoRefresh() {
		this.stopAutoRefresh();
	}

	enableAutoRefresh() {
		if (this.currentGameUrl && !this.refreshInterval) {
			this.startAutoRefresh();
		}
	}
}

// Auth Store using SDK
export class SDKAuthStore {
	public state = $state<{
		user: AuthUser | null;
		accessToken: string | null;
		isAuthenticated: boolean;
		loading: boolean;
		error: string | null;
	}>({
		user: null,
		accessToken: null,
		isAuthenticated: false,
		loading: false,
		error: null
	});

	private refreshTimer: number | null = null;

	constructor() {
		this.restoreAuthState();
	}

	// Getters
	get user() {
		return this.state.user;
	}

	get accessToken() {
		return this.state.accessToken;
	}

	get isAuthenticated() {
		return this.state.isAuthenticated;
	}

	get loading() {
		return this.state.loading;
	}

	get error() {
		return this.state.error;
	}

	async login(credentials: { type: 'username_password' | 'mobile_otp' | 'heritage_sso'; username?: string; password?: string; mobile?: string; otp?: string; token?: string; email?: string }) {
		if (this.state.loading) return;

		// Check if game is loaded
		if (!gameStore.data?.id) {
			this.state.error = 'No game loaded. Please load a game first.';
			return;
		}

		this.state.loading = true;
		this.state.error = null;

		try {
			const fullCredentials = {
				...credentials,
				gameId: gameStore.data.id
			};

			const tokens = await sdk.auth.login(fullCredentials);
			
			const decoded = sdk.auth.decodeToken(tokens.accessToken);
			if (!decoded) {
				throw new Error('Invalid token received');
			}

			const user: AuthUser = {
				...decoded
			};

			this.state.user = user;
			this.state.accessToken = tokens.accessToken;
			this.state.isAuthenticated = true;

			this.saveAuthState();
			this.setupTokenRefresh();

		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'Login failed';
			this.clearAuthState();
		} finally {
			this.state.loading = false;
		}
	}

	async logout() {
		this.state.loading = true;

		try {
			await sdk.auth.logout();
		} catch (error) {
			console.error('Logout error:', error);
		} finally {
			this.clearAuthState();
			this.state.loading = false;
		}
	}

	clearError() {
		this.state.error = null;
	}

	private setupTokenRefresh() {
		if (this.refreshTimer) {
			clearInterval(this.refreshTimer);
		}

		this.refreshTimer = window.setInterval(
			async () => {
				if (this.state.accessToken && this.state.user) {
					if (sdk.auth.isTokenExpired(this.state.accessToken)) {
						const userId = this.getUserId();
						if (userId) {
							try {
								const tokens = await sdk.auth.refreshToken({
									userId,
									loginType: this.state.user.type
								});
								this.state.accessToken = tokens.accessToken;
								this.state.error = null;
								this.saveAuthState();
							} catch (error) {
								console.error('Token refresh failed:', error);
								this.logout();
							}
						}
					}
				}
			},
			8 * 60 * 1000
		);
	}

	private getUserId(): string | null {
		if (!this.state.user) return null;

		switch (this.state.user.type) {
			case 'username_password':
				return this.state.user.username || null;
			case 'mobile_otp':
				return this.state.user.mobile || null;
			case 'heritage_sso':
				return this.state.user.email || null;
			default:
				return null;
		}
	}

	private saveAuthState() {
		if (typeof window !== 'undefined') {
			const authData = {
				user: this.state.user,
				accessToken: this.state.accessToken,
				isAuthenticated: this.state.isAuthenticated
			};
			localStorage.setItem('auth', JSON.stringify(authData));
		}
	}

	private restoreAuthState() {
		if (typeof window !== 'undefined') {
			try {
				const stored = localStorage.getItem('auth');
				if (stored) {
					const authData = JSON.parse(stored);
					
					if (authData.accessToken && !sdk.auth.isTokenExpired(authData.accessToken)) {
						this.state.user = authData.user;
						this.state.accessToken = authData.accessToken;
						this.state.isAuthenticated = authData.isAuthenticated;
						this.setupTokenRefresh();
					} else {
						this.clearAuthState();
					}
				}
			} catch (error) {
				console.error('Error restoring auth state:', error);
				this.clearAuthState();
			}
		}
	}

	private clearAuthState() {
		this.state.user = null;
		this.state.accessToken = null;
		this.state.isAuthenticated = false;
		this.state.error = null;

		if (this.refreshTimer) {
			clearInterval(this.refreshTimer);
			this.refreshTimer = null;
		}

		if (typeof window !== 'undefined') {
			localStorage.removeItem('auth');
		}
	}
}

// Create store instances
export const gameStore = new SDKGameStore();
export const authStore = new SDKAuthStore();

// Export SDK for direct access if needed
export { sdk };
