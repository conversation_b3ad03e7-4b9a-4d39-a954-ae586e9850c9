<script lang="ts">
	import { useAuth } from '$lib/composables/sdk-composables';
	import type { LoginType } from '$lib/sdk';

	interface Props {
		loginType: LoginType;
	}

	let { loginType }: Props = $props();

	const auth = useAuth();

	// Form state
	let username = $state('');
	let password = $state('');
	let mobile = $state('');
	let otp = $state('');
	let ssoToken = $state('');
	let email = $state('');

	async function handleSubmit() {
		auth.clearError();

		try {
			switch (loginType) {
				case 'username_password':
					await auth.login({
						type: 'username_password',
						username,
						password
					});
					break;
				case 'mobile_otp':
					await auth.login({
						type: 'mobile_otp',
						mobile,
						otp
					});
					break;
				case 'heritage_sso':
					await auth.login({
						type: 'heritage_sso',
						token: ssoToken,
						email
					});
					break;
			}
		} catch (error) {
			// Error is handled by the auth store
		}
	}
</script>

<div class="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
	<h2 class="text-2xl font-bold mb-6 text-center">
		{#if loginType === 'username_password'}
			Login with Username & Password
		{:else if loginType === 'mobile_otp'}
			Login with Mobile & OTP
		{:else if loginType === 'heritage_sso'}
			Login with Heritage SSO
		{/if}
	</h2>

	{#if auth.error}
		<div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
			{auth.error}
		</div>
	{/if}

	<form onsubmit={handleSubmit} class="space-y-4">
		{#if loginType === 'username_password'}
			<div>
				<label for="username" class="block text-sm font-medium text-gray-700 mb-1">
					Username
				</label>
				<input
					id="username"
					type="text"
					bind:value={username}
					required
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="password" class="block text-sm font-medium text-gray-700 mb-1">
					Password
				</label>
				<input
					id="password"
					type="password"
					bind:value={password}
					required
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
		{:else if loginType === 'mobile_otp'}
			<div>
				<label for="mobile" class="block text-sm font-medium text-gray-700 mb-1">
					Mobile Number
				</label>
				<input
					id="mobile"
					type="tel"
					bind:value={mobile}
					required
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="otp" class="block text-sm font-medium text-gray-700 mb-1">
					OTP Code
				</label>
				<input
					id="otp"
					type="text"
					bind:value={otp}
					required
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
		{:else if loginType === 'heritage_sso'}
			<div>
				<label for="email" class="block text-sm font-medium text-gray-700 mb-1">
					Email
				</label>
				<input
					id="email"
					type="email"
					bind:value={email}
					required
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="ssoToken" class="block text-sm font-medium text-gray-700 mb-1">
					SSO Token
				</label>
				<input
					id="ssoToken"
					type="text"
					bind:value={ssoToken}
					required
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
		{/if}

		<button
			type="submit"
			disabled={auth.loading}
			class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
		>
			{#if auth.loading}
				Logging in...
			{:else}
				Login
			{/if}
		</button>
	</form>
</div>
