<script lang="ts">
	import { useAuth } from '$lib/composables/sdk-composables';

	const auth = useAuth();

	async function handleLogout() {
		await auth.logout();
	}
</script>

{#if auth.isAuthenticated && auth.user}
	<div class="bg-white p-4 rounded-lg shadow-md">
		<div class="flex items-center justify-between">
			<div>
				<h3 class="text-lg font-semibold text-gray-900">Welcome!</h3>
				<div class="text-sm text-gray-600">
					{#if auth.user.type === 'username_password'}
						<p>Username: {auth.user.username}</p>
					{:else if auth.user.type === 'mobile_otp'}
						<p>Mobile: {auth.user.mobile}</p>
					{:else if auth.user.type === 'heritage_sso'}
						<p>Email: {auth.user.email}</p>
					{/if}
					<p>Login Type: {auth.user.type}</p>
				</div>
			</div>
			<button
				onclick={handleLogout}
				disabled={auth.loading}
				class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
			>
				{#if auth.loading}
					Logging out...
				{:else}
					Logout
				{/if}
			</button>
		</div>
	</div>
{:else}
	<div class="bg-gray-100 p-4 rounded-lg">
		<p class="text-gray-600">Not authenticated</p>
	</div>
{/if}
