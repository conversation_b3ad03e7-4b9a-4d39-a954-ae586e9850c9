import { getContext, setContext } from 'svelte';
import { gameStore, authStore, type SDKGameStore, type SDKAuthStore } from '$lib/stores/sdk-stores.svelte';

const GAME_CONTEXT_KEY = Symbol('sdk-game');
const AUTH_CONTEXT_KEY = Symbol('sdk-auth');

// Game Context
export function setGameContext(): SDKGameStore {
	setContext(GAME_CONTEXT_KEY, gameStore);
	return gameStore;
}

export function getGameContext(): SDKGameStore {
	const context = getContext<SDKGameStore>(GAME_CONTEXT_KEY);
	if (!context) {
		throw new Error('Game context not found. Make sure you are using this within a game layout.');
	}
	return context;
}

// Auth Context
export function setAuthContext(): SDKAuthStore {
	setContext(AUTH_CONTEXT_KEY, authStore);
	return authStore;
}

export function getAuthContext(): SDKAuthStore {
	const context = getContext<SDKAuthStore>(AUTH_CONTEXT_KEY);
	if (!context) {
		throw new Error('Auth context not found. Make sure you are using this within an auth provider.');
	}
	return context;
}
