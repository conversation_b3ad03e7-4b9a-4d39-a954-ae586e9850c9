<script lang="ts">
	import { page } from '$app/state';
	import { setGameContext } from '$lib/context/sdk-context';
	import type { LayoutProps } from './$types';

	let { children }: LayoutProps = $props();

	// Set up game context for child components using SDK
	const gameStore = setGameContext();

	// Load game data when gameUrl changes
	let currentGameUrl = $state<string | undefined>(undefined);

	$effect(() => {
		const gameUrl = page.params.gameUrl;
		if (gameUrl !== currentGameUrl) {
			currentGameUrl = gameUrl;
			if (gameUrl) {
				gameStore.loadGame(gameUrl);
			} else {
				gameStore.reset();
			}
		}
	});
</script>

<sgds-masthead></sgds-masthead>

{#if page.params.gameUrl}
	{#if gameStore.loading}
		<div class="flex items-center justify-center p-8">
			<p class="text-lg">Loading game...</p>
		</div>
	{:else if gameStore.error}
		<div class="flex items-center justify-center p-8">
			<p class="text-lg text-red-600">Error: {gameStore.error}</p>
		</div>
	{:else if gameStore.isReady}
		{@render children()}
	{/if}
{:else}
	<div class="flex items-center justify-center p-8">
		<p class="text-lg">No game specified</p>
	</div>
{/if}
