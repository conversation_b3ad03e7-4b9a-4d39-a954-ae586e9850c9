<script lang="ts">
	import { useGame, useAuth } from '$lib/composables/sdk-composables';
	import LoginForm from '$lib/components/auth/LoginForm.svelte';
	import UserProfile from '$lib/components/auth/UserProfile.svelte';

	const game = useGame();
	const auth = useAuth();
</script>

<div class="p-6 space-y-8">
	<h1 class="mb-4 text-2xl font-bold">Game: {game.data?.name}</h1>

	{#if game.data}
		<!-- Authentication Section -->
		<div class="bg-gray-50 p-6 rounded-lg">
			<h2 class="text-xl font-semibold mb-4">Authentication</h2>

			{#if auth.isAuthenticated}
				<UserProfile />
			{:else}
				<div class="space-y-4">
					<p class="text-gray-600">Please log in to access game features.</p>
					<p class="text-sm text-gray-500">
						Game ID: <code class="bg-gray-200 px-2 py-1 rounded">{game.data.id}</code>
						(automatically used for authentication)
					</p>

					{#if game.data.login_type === 'username_password' || game.data.login_type === 'mobile_otp' || game.data.login_type === 'heritage_sso'}
						<LoginForm loginType={game.data.login_type} />
					{:else}
						<p class="text-red-600">
							Unsupported login type: {game.data.login_type}.
							Supported types: username_password, mobile_otp, heritage_sso
						</p>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Game Information -->
		<div class="bg-white p-6 rounded-lg shadow">
			<h2 class="mb-4 text-xl font-semibold">Game Information</h2>
			<div class="space-y-4">
				<p><strong>URL:</strong> {game.data.url}</p>
				<p><strong>Start Date:</strong> {game.data.start_date}</p>
				<p><strong>End Date:</strong> {game.data.end_date}</p>
				<p><strong>Login Type:</strong> {game.data.login_type}</p>

				{#if game.data.task_categories.length > 0}
					<div>
						<h3 class="mb-2 text-lg font-semibold">Task Categories</h3>
						<ul class="list-inside list-disc">
							{#each game.data.task_categories as category}
								<li>{category.name} ({category.tasks.length} tasks)</li>
							{/each}
						</ul>
					</div>
				{/if}
			</div>
		</div>

		<!-- Protected Content (only show if authenticated) -->
		{#if auth.isAuthenticated}
			<div class="bg-green-50 p-6 rounded-lg border border-green-200">
				<h2 class="text-xl font-semibold text-green-800 mb-4">🎮 Game Content</h2>
				<p class="text-green-700">
					Welcome! You are now authenticated and can access protected game features.
				</p>
				<p class="text-sm text-green-600 mt-2">
					This content is only visible to authenticated users.
				</p>
			</div>
		{:else}
			<div class="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
				<h2 class="text-xl font-semibold text-yellow-800 mb-4">🔒 Protected Content</h2>
				<p class="text-yellow-700">
					Please log in to access game features and content.
				</p>
			</div>
		{/if}
	{/if}
</div>
