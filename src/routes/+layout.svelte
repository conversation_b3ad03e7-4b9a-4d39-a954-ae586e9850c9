<script lang="ts">
	import '../app.css';
	import favicon from '$lib/assets/favicon.svg';
	import { setAuthContext } from '$lib/context/sdk-context';

	let { children } = $props();

	// Set up auth context for the entire app using SDK
	setAuthContext();
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
</svelte:head>

<div class="w-screen h-dvh overflow-hidden">
	{@render children?.()}
</div>
